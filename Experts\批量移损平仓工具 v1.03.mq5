//+------------------------------------------------------------------+
//|                                       批量移损平仓工具 v1.03.mq5 |
//|                                  Copyright 2025, MetaQuotes Ltd. |
//|                                             https://www.mql5.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2025, MetaQuotes Ltd."
#property link      "https://www.mql5.com"
#property version   "1.03"
#include <Controls\Button.mqh>
#include <ChartObjects\ChartObjectsLines.mqh>
#include <Controls\Dialog.mqh>
#include <Trade\Trade.mqh>
#include <Trade\PositionInfo.mqh>
#include <Arrays\ArrayLong.mqh>

// EA参数 - 只添加魔术号功能
input ulong MagicNumber = 0;         // 魔术号（0表示处理所有魔术号）

// 按钮对象
CButton btnSL;
CButton btnNewSL;
CButton btnCloseRange;

// 平仓模块对象 - 基于ClosePositionsModule封装函数
CTrade         m_trade;
CPositionInfo  m_position;
CArrayLong     m_arr_tickets;

// 定义RTOTAL和SLEEPTIME
#define RTOTAL 3
#define SLEEPTIME 1000

// 【性能优化】批处理配置常量
#define BATCH_PROCESS_SIZE 5      // 每批处理的订单数量
#define PROGRESS_UPDATE_INTERVAL 10  // 进度更新间隔
#define MAX_RETRY_ATTEMPTS 3      // 最大重试次数

// 水平线名称
string sl1_name = "sl_1";
string sl2_name = "sl_2";
string newsl_name = "new_sl";

// 状态变量
bool isCreatingHLine = false;    // 是否处于创建水平线模式
string currentHLineName = "";   // 当前水平线的名称

// 按钮尺寸和位置设置
int button_width = 80;    // 按钮宽度
int button_height = 35;    // 按钮高度
int margin_horizontal = 92;  // 水平边距，增加此值使按钮往左移
int margin_vertical = 80;    // 垂直边距，增加此值使按钮往上移

int OnInit()
{
   // 启用鼠标移动事件
   ChartSetInteger(0, CHART_EVENT_MOUSE_MOVE, true);

   // 创建New SL按钮（上方按钮）
   if(!btnNewSL.Create(0, "btnNewSL", 0, margin_horizontal, margin_vertical + button_height + 10, button_width, button_height))
      return(INIT_FAILED);
   btnNewSL.Text("New SL");
   btnNewSL.Font("Arial");
   btnNewSL.FontSize(11); 
   btnNewSL.Color(clrBlack);
   btnNewSL.ColorBackground(clrMediumOrchid);
   btnNewSL.ColorBorder(clrGray);
   btnNewSL.Enable();

   // 设置按钮对齐方式为右下角 
   ObjectSetInteger(0, "btnNewSL", OBJPROP_CORNER, CORNER_RIGHT_LOWER);

   // 创建SL按钮（底部按钮）
   if(!btnSL.Create(0, "btnSL", 0, margin_horizontal, margin_vertical, button_width, button_height))
      return(INIT_FAILED);
   btnSL.Text("Move SL");
   btnSL.Font("Arial");
   btnSL.FontSize(11);
   btnSL.Color(clrBlack);
   btnSL.ColorBackground(clrOrange);
   btnSL.ColorBorder(clrGray);
   btnSL.Enable();

   // 设置按钮对齐方式为右下角 
   ObjectSetInteger(0, "btnSL", OBJPROP_CORNER, CORNER_RIGHT_LOWER);

   // 创建区间平仓按钮（最上方按钮）
   if(!btnCloseRange.Create(0, "btnCloseRange", 0, margin_horizontal, margin_vertical + (button_height + 10) * 2, button_width, button_height))
      return(INIT_FAILED);
   btnCloseRange.Text("区间平仓");
   btnCloseRange.Font("Arial");
   btnCloseRange.FontSize(11);
   btnCloseRange.Color(clrBlack);
   btnCloseRange.ColorBackground(clrRed);
   btnCloseRange.ColorBorder(clrGray);
   btnCloseRange.Enable();

   // 设置按钮对齐方式为右下角
   ObjectSetInteger(0, "btnCloseRange", OBJPROP_CORNER, CORNER_RIGHT_LOWER);

   ChartRedraw(); 
   return(INIT_SUCCEEDED);
}

void OnClickSLButton()
{
    // 删除已存在的线（批量处理）
    ObjectDelete(0, sl1_name);
    ObjectDelete(0, sl2_name);
    ObjectDelete(0, newsl_name);

    // 获取图表价格范围
    double high = ChartGetDouble(0, CHART_PRICE_MAX);
    double low = ChartGetDouble(0, CHART_PRICE_MIN);
    double middle_price = (high + low) / 2.0;
    double line_spacing = (high - low) * 0.05;

    // 批量创建水平线（无需立即刷新）
    CreateHorizontalLine(sl1_name, middle_price, clrGold);
    CreateHorizontalLine(sl2_name, middle_price + line_spacing, clrGold);
    CreateHorizontalLine(newsl_name, middle_price - line_spacing, clrDeepPink);

    // 统一刷新图表
    ChartRedraw();
}

// 检查订单是否匹配当前品种和魔术号 - 新增功能
inline bool IsMatchingPosition(ulong ticket)
{
    if(!PositionSelectByTicket(ticket))
        return false;
        
    // 检查品种
    string position_symbol = PositionGetString(POSITION_SYMBOL);
    if(position_symbol != _Symbol)
        return false;
        
    // 检查魔术号（0表示处理所有魔术号）
    if(MagicNumber != 0)
    {
        ulong position_magic = PositionGetInteger(POSITION_MAGIC);
        if(position_magic != MagicNumber)
            return false;
    }
    
    return true;
}

// 检查订单是否已经保本 - 新增功能
bool IsBreakevenPosition(ulong ticket)
{
    if(!PositionSelectByTicket(ticket))
        return false;
        
    double open_price = PositionGetDouble(POSITION_PRICE_OPEN);
    double current_sl = PositionGetDouble(POSITION_SL);
    ENUM_POSITION_TYPE pos_type = (ENUM_POSITION_TYPE)PositionGetInteger(POSITION_TYPE);
    
    if(current_sl <= 0)
        return false; // 没有止损，不算保本
    
    if(pos_type == POSITION_TYPE_BUY)
    {
        // 买单：止损价格 >= 开仓价格 才算保本
        return current_sl >= open_price;
    }
    else // POSITION_TYPE_SELL
    {
        // 卖单：止损价格 <= 开仓价格 才算保本
        return current_sl <= open_price;
    }
}

// 点击New SL按钮，移动止损 - 基于初始版本，增强为保本订单
void OnClickNewSLButton()
{
    // 获取三条线的价格位置 - 严格保持初始版本
    double sl1_price = ObjectGetDouble(0, sl1_name, OBJPROP_PRICE);
    double sl2_price = ObjectGetDouble(0, sl2_name, OBJPROP_PRICE);
    double new_sl_price = ObjectGetDouble(0, newsl_name, OBJPROP_PRICE);

    if(sl1_price == 0 || sl2_price == 0 || new_sl_price == 0)
    {
        Print("错误: 未找到所有水平线");
        return;
    }

    // 确保sl1_price和sl2_price的顺序正确 - 严格保持初始版本
    double lower_price = MathMin(sl1_price, sl2_price);
    double upper_price = MathMax(sl1_price, sl2_price);

    // 【性能优化】预分配数组空间，避免循环内频繁内存分配
    int total_positions = PositionsTotal();
    ulong tickets[];
    ArrayResize(tickets, total_positions); // 预分配最大可能空间
    int count = 0;

    // 【性能优化】用于批量日志输出的数组
    string found_tickets_info[];
    ArrayResize(found_tickets_info, total_positions);
    int info_count = 0;

    // 添加进度提示
    Comment("正在扫描保本订单...");

    // 先收集所有符合条件的保本订单票号
    for(int i=0; i<total_positions; i++)
    {
        ulong ticket = PositionGetTicket(i);

        // 使用新的匹配函数检查品种和魔术号
        if(!IsMatchingPosition(ticket))
            continue;

        double current_sl = PositionGetDouble(POSITION_SL);

        // 检查是否为保本订单且止损在指定范围内
        bool is_breakeven = IsBreakevenPosition(ticket);
        bool in_range = (current_sl > 0 && current_sl >= lower_price && current_sl <= upper_price);

        if(is_breakeven && in_range)
        {
            // 【性能优化】直接赋值，避免数组调整
            tickets[count] = ticket;

            // 【性能优化】收集日志信息，稍后批量输出
            found_tickets_info[info_count] = "找到保本订单 " + IntegerToString(ticket) + "，当前止损: " + DoubleToString(current_sl, _Digits);
            info_count++;
            count++;
        }

        // 【性能优化】每处理PROGRESS_UPDATE_INTERVAL个订单释放一次线程，避免UI冻结
        if(i % PROGRESS_UPDATE_INTERVAL == 0 && i > 0)
        {
            Comment("正在扫描保本订单... " + IntegerToString(i) + "/" + IntegerToString(total_positions));
            Sleep(0);
        }
    }

    // 【性能优化】调整数组到实际大小
    ArrayResize(tickets, count);

    // 【性能优化】批量输出找到的订单信息
    if(info_count > 0)
    {
        Print("=== 找到的保本订单信息 ===");
        for(int i=0; i<info_count; i++)
        {
            Print(found_tickets_info[i]);
        }
        Print("=== 共找到 ", count, " 个符合条件的保本订单 ===");
    }
    else
    {
        Print("未找到符合条件的保本订单");
        Comment("");
        return;
    }

    // 添加修改进度提示
    Comment("正在修改止损订单: 0/" + IntegerToString(count));

    // 处理收集到的所有订单 - 严格保持初始版本
    int modified = 0;
    for(int i=0; i<count; i++)
    {
        ulong ticket = tickets[i];
        if(PositionSelectByTicket(ticket))
        {
            double current_sl = PositionGetDouble(POSITION_SL);

            // 直接修改止损到新位置 - 严格保持初始版本
            if(TradeModify(ticket, PositionGetDouble(POSITION_PRICE_OPEN), new_sl_price, PositionGetDouble(POSITION_TP)))
            {
                modified++;
                //Print("修改订单 ", ticket, " 止损从 ", current_sl, " 到 ", new_sl_price);
            }
            else
            {
                Print("修改订单 ", ticket, " 失败，错误代码: ", GetLastError());
            }
        }

        // 【性能优化】每处理BATCH_PROCESS_SIZE个订单释放线程并更新进度
        if(i % BATCH_PROCESS_SIZE == 0)
        {
            Comment("正在修改止损订单: " + IntegerToString(i+1) + "/" + IntegerToString(count));
            Sleep(0);
        }
    }

    // 清除进度提示并输出最终结果
    Comment("");
    Print("操作完成。共修改了 ", modified, " 个保本订单的止损，总共找到 ", count, " 个符合条件的保本订单");
}

// 区间平仓函数 - 基于ClosePositionsModule封装函数
bool ClosePositionsInRange()
{
    // 获取两条线的价格位置
    double sl1_price = ObjectGetDouble(0, sl1_name, OBJPROP_PRICE);
    double sl2_price = ObjectGetDouble(0, sl2_name, OBJPROP_PRICE);

    // 检查水平线是否存在
    if(sl1_price == 0 || sl2_price == 0)
    {
        Print("错误: 未找到水平线，请先创建水平线");
        return false;
    }

    // 确保价格顺序正确
    double lower_price = MathMin(sl1_price, sl2_price);
    double upper_price = MathMax(sl1_price, sl2_price);

    //Print("区间平仓: 价格范围 [", DoubleToString(lower_price, _Digits), " - ", DoubleToString(upper_price, _Digits), "] 品种: ", _Symbol);

    // 一次性设置所有交易参数 - 基于ClosePositionsModule
    m_trade.SetDeviationInPoints(INT_MAX);
    m_trade.SetAsyncMode(true);
    m_trade.SetMarginMode();
    m_trade.LogLevel(LOG_LEVEL_ERRORS);

    // 缓存当前品种，避免重复字符串比较
    string currentSymbol = _Symbol;

    for(uint retry = 0; retry < RTOTAL && !IsStopped(); retry++)
    {
        bool result = true;
        m_arr_tickets.Shutdown();

        // 预先获取持仓总数，避免在循环中重复调用
        int posTotal = PositionsTotal();

        // 收集区间内的订单
        for(int i = 0; i < posTotal && !IsStopped(); i++)
        {
            if(m_position.SelectByIndex(i))
            {
                // 只处理当前图表品种和魔术号的订单
                if(m_position.Symbol() == currentSymbol)
                {
                    // 检查魔术号（0表示处理所有魔术号）
                    if(MagicNumber != 0 && m_position.Magic() != MagicNumber)
                        continue;

                    double open_price = m_position.PriceOpen(); // 获取开仓价格

                    // 检查开仓价格是否在指定区间内
                    if(open_price >= lower_price && open_price <= upper_price)
                    {
                        m_arr_tickets.Add(m_position.Ticket());
                    }
                }
            }
        }

        int ticketsTotal = m_arr_tickets.Total();
        if(ticketsTotal == 0)
        {
            Print("当前品种 ", currentSymbol, " 在指定价格范围内没有持仓");
            return true;
        }
        
        // 缓存常用值，避免重复获取
        int freeze_level = (int)SymbolInfoInteger(currentSymbol, SYMBOL_TRADE_FREEZE_LEVEL);
        double point = SymbolInfoDouble(currentSymbol, SYMBOL_POINT);
        double freeze_distance = freeze_level * point;

        // 【性能优化】用于批量日志输出
        string failed_tickets[];
        string frozen_tickets[];
        int failed_count = 0;
        int frozen_count = 0;
        int closed_count = 0;

        ArrayResize(failed_tickets, ticketsTotal);
        ArrayResize(frozen_tickets, ticketsTotal);

        // 添加进度提示
        Comment("正在平仓区间内订单: 0/" + IntegerToString(ticketsTotal));

        // 平掉区间内的订单
        for(int i = 0; i < ticketsTotal && !IsStopped(); i++)
        {
            ulong ticket = m_arr_tickets.At(i);
            if(m_position.SelectByTicket(ticket))
            {
                double priceCurrent = m_position.PriceCurrent();
                double takeProfit = m_position.TakeProfit();
                double stopLoss = m_position.StopLoss();

                bool TP_check = (MathAbs(priceCurrent - takeProfit) > freeze_distance);
                bool SL_check = (MathAbs(priceCurrent - stopLoss) > freeze_distance);

                if(TP_check && SL_check)
                {
                    m_trade.SetExpertMagicNumber(m_position.Magic());
                    m_trade.SetTypeFillingBySymbol(m_position.Symbol());
                    if(!m_trade.PositionClose(ticket) || (m_trade.ResultRetcode() != TRADE_RETCODE_DONE && m_trade.ResultRetcode() != TRADE_RETCODE_PLACED))
                    {
                        // 【性能优化】收集失败信息，稍后批量输出
                        failed_tickets[failed_count] = "平仓订单 " + IntegerToString(ticket) + " 失败. 错误代码: " + IntegerToString(m_trade.ResultRetcode()) + " (" + m_trade.ResultComment() + ")";
                        failed_count++;
                        result = false;
                    }
                    else
                    {
                        closed_count++;
                    }
                }
                else
                {
                    // 【性能优化】收集冻结信息，稍后批量输出
                    frozen_tickets[frozen_count] = "订单 " + IntegerToString(ticket) + " 平仓被禁止. 止损或止盈太接近激活价格 [FROZEN].";
                    frozen_count++;
                    result = false;
                }
            }

            // 【性能优化】每处理5个订单更新进度并释放线程
            if(i % 5 == 0)
            {
                Comment("正在平仓区间内订单: " + IntegerToString(i+1) + "/" + IntegerToString(ticketsTotal));
                Sleep(0);
            }
        }

        // 【性能优化】批量输出结果信息
        Comment("");
        if(closed_count > 0 || failed_count > 0 || frozen_count > 0)
        {
            Print("=== 区间平仓操作结果 ===");
            Print("成功平仓: ", closed_count, " 个订单");

            if(failed_count > 0)
            {
                Print("--- 平仓失败的订单 ---");
                for(int i=0; i<failed_count; i++)
                {
                    Print(failed_tickets[i]);
                }
            }

            if(frozen_count > 0)
            {
                Print("--- 被冻结的订单 ---");
                for(int i=0; i<frozen_count; i++)
                {
                    Print(frozen_tickets[i]);
                }
            }
            Print("=== 区间平仓操作完成 ===");
        }

        if(result || ticketsTotal == 0)
            break;

        Sleep(SLEEPTIME);
    }
    return true;
}

// 创建水平线
void CreateHorizontalLine(const string name, const double price, const color clr)
{
    if(ObjectFind(0, name) == -1)
    {
        ObjectCreate(0, name, OBJ_HLINE, 0, 0, price);
        ObjectSetInteger(0, name, OBJPROP_COLOR, clr);
        ObjectSetInteger(0, name, OBJPROP_STYLE, STYLE_DASH);
        ObjectSetInteger(0, name, OBJPROP_WIDTH, 2);
        ObjectSetInteger(0, name, OBJPROP_BACK, false);       // 设置为前景，使线更明显
        ObjectSetInteger(0, name, OBJPROP_SELECTABLE, true);  // 确保可选
        ObjectSetInteger(0, name, OBJPROP_SELECTED, true);    // 默认选中状态
        ObjectSetInteger(0, name, OBJPROP_TIMEFRAMES, OBJ_ALL_PERIODS);
        ObjectSetInteger(0, name, OBJPROP_ZORDER, 100);       // 设置较高的Z顺序，使线在前面
    }
    else
    {
        ObjectMove(0, name, 0, 0, price);
        ObjectSetInteger(0, name, OBJPROP_COLOR, clr);
        ObjectSetInteger(0, name, OBJPROP_SELECTED, true);    // 更新时也设置为选中状态
    }
}

// 修改订单止损和止盈 - 【性能优化版本】
bool TradeModify(ulong ticket, double entry_price, double sl, double tp)
{
    // 再次检查订单是否存在
    if(!PositionSelectByTicket(ticket))
    {
        Print("错误: 无法选择订单 ", ticket, "，可能已关闭");
        return false;
    }

    // 【性能优化】缓存订单信息，避免重复查询
    string symbol = PositionGetString(POSITION_SYMBOL);
    double current_sl = PositionGetDouble(POSITION_SL);

    // 检查止损是否已经是目标值（避免重复修改）
    if(MathAbs(current_sl - sl) < _Point)
    {
        //Print("订单 ", ticket, " 的止损已经是 ", sl, "，无需修改");
        return true; // 返回true，因为实际上已经是正确的值
    }

    // 【性能优化】检查冻结距离，避免无效请求
    double point = SymbolInfoDouble(symbol, SYMBOL_POINT);
    int freeze_level = (int)SymbolInfoInteger(symbol, SYMBOL_TRADE_FREEZE_LEVEL);
    double freeze_distance = freeze_level * point;
    double current_price = PositionGetDouble(POSITION_PRICE_CURRENT);

    if(MathAbs(current_price - sl) <= freeze_distance)
    {
        Print("订单 ", ticket, " 止损距离当前价格过近，被冻结距离限制");
        return false;
    }

    MqlTradeRequest request = {};
    MqlTradeResult result = {};

    request.action = TRADE_ACTION_SLTP;
    request.position = ticket;
    request.symbol = symbol; // 使用缓存的符号
    request.sl = sl;
    request.tp = tp;
    request.deviation = 5;

    // 【性能优化】添加重试机制，最多重试MAX_RETRY_ATTEMPTS次
    int max_retries = MAX_RETRY_ATTEMPTS;
    bool success = false;

    for(int retry = 0; retry < max_retries && !success; retry++)
    {
        success = OrderSend(request, result);

        if(success && result.retcode == TRADE_RETCODE_DONE)
        {
            //Print("订单 ", ticket, " 止损修改成功: ", current_sl, " -> ", sl);
            return true;
        }
        else if(result.retcode == TRADE_RETCODE_PRICE_CHANGED && retry < max_retries - 1)
        {
            // 价格变化，稍等后重试
            Sleep(100);
            continue;
        }
        else if(result.retcode == TRADE_RETCODE_TOO_MANY_REQUESTS && retry < max_retries - 1)
        {
            // 请求过于频繁，等待后重试
            Sleep(500);
            continue;
        }
        else
        {
            break; // 其他错误，不重试
        }
    }

    // 详细记录修改结果
    if(success)
    {
        Print("订单 ", ticket, " 止损修改失败: ", GetErrorDescription(result.retcode));
        return false;
    }
    else
    {
        Print("订单 ", ticket, " 止损修改失败: ", GetErrorDescription(GetLastError()));
        return false;
    }
}

// 获取常见错误的中文描述（简化版）
string GetErrorDescription(int error_code)
{
    switch(error_code)
    {
        // 成功状态码
        case TRADE_RETCODE_DONE:            return "请求完成";
        case TRADE_RETCODE_PLACED:          return "订单已放置";
        // 最常见的交易错误代码
        case TRADE_RETCODE_INVALID_STOPS:   return "无效的止损/止盈";
        case TRADE_RETCODE_INVALID_PRICE:   return "无效的价格";
        case TRADE_RETCODE_MARKET_CLOSED:   return "市场关闭";
        case TRADE_RETCODE_PRICE_CHANGED:   return "价格改变";
        case TRADE_RETCODE_REJECT:          return "请求被拒绝";
        case TRADE_RETCODE_TOO_MANY_REQUESTS: return "请求过于频繁";
        case TRADE_RETCODE_CONNECTION:      return "无连接";
        default: return "错误代码: " + IntegerToString(error_code);
    }
}

void OnChartEvent(const int id, const long &lparam, const double &dparam, const string &sparam)
{
    // 处理按钮点击事件
    if(id == CHARTEVENT_OBJECT_CLICK)
    {
        if(sparam == "btnSL")
        {
            OnClickSLButton();
            return;
        }
        else if(sparam == "btnNewSL")
        {
            OnClickNewSLButton();
            return;
        }
        else if(sparam == "btnCloseRange")
        {
            ClosePositionsInRange();
            return;
        }
    }

    // 处理图表大小变化事件
    if(id == CHARTEVENT_CHART_CHANGE)
    {
        // 获取新的图表尺寸
        long chart_width = ChartGetInteger(0, CHART_WIDTH_IN_PIXELS);
        long chart_height = ChartGetInteger(0, CHART_HEIGHT_IN_PIXELS);

        // 重新设置按钮位置（通过锚点自动调整）
        // 注意：垂直排列按钮，从下到上排列
        btnSL.Alignment(WND_ALIGN_RIGHT | WND_ALIGN_BOTTOM, 0, 0, margin_horizontal, margin_vertical);
        btnNewSL.Alignment(WND_ALIGN_RIGHT | WND_ALIGN_BOTTOM, 0, 0, margin_horizontal, margin_vertical + button_height + 10);
        btnCloseRange.Alignment(WND_ALIGN_RIGHT | WND_ALIGN_BOTTOM, 0, 0, margin_horizontal, margin_vertical + (button_height + 10) * 2);

        // 确保按钮大小正确
        btnSL.Width(button_width);
        btnSL.Height(button_height);
        btnNewSL.Width(button_width);
        btnNewSL.Height(button_height);
        btnCloseRange.Width(button_width);
        btnCloseRange.Height(button_height);

        ChartRedraw();
        return;
    }

    // 处理鼠标移动（创建水平线模式）
    if(isCreatingHLine && id == CHARTEVENT_MOUSE_MOVE)
    {
        double price = 0;
        datetime time = 0;
        int sub_window = 0;

        if(ChartXYToTimePrice(0, (int)lparam, (int)dparam, sub_window, time, price))
        {
            ObjectMove(0, currentHLineName, 0, 0, price);
            ChartRedraw();
        }
        return;
    }

    // 处理鼠标点击事件（固定线）
    if(isCreatingHLine && id == CHARTEVENT_CLICK)
    {
        // 避免误点击按钮
        if(sparam == "btnSL" || sparam == "btnNewSL" || sparam == "btnCloseRange")
            return;

        isCreatingHLine = false;
        ObjectSetInteger(0, currentHLineName, OBJPROP_SELECTED, 0);
        ChartRedraw();
    }
}

void OnDeinit(const int reason)
{
    // 删除按钮对象
    btnSL.Destroy(reason);
    btnNewSL.Destroy(reason);
    btnCloseRange.Destroy(reason);
     
    // --- 仅在 EA 被手动移除时删除水平线
    if(reason == REASON_REMOVE)
    {
        // 或者也可以考虑 REASON_CHARTCLOSE
        // if(reason == REASON_REMOVE || reason == REASON_CHARTCLOSE)
        // 删除 EA 创建的水平线对象
        if(ObjectFind(0, sl1_name) != -1)
            ObjectDelete(0, sl1_name);
        if(ObjectFind(0, sl2_name) != -1)
            ObjectDelete(0, sl2_name);
        if(ObjectFind(0, newsl_name) != -1)
            ObjectDelete(0, newsl_name);
    }   
    // 强制重绘图表
    ChartRedraw();
}
