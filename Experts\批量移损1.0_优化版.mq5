//+------------------------------------------------------------------+
//|                                                     批量移损.mq5 |
//|                                  Copyright 2025, MetaQuotes Ltd. |
//|                                             https://www.mql5.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2025, MetaQuotes Ltd."
#property link      "https://www.mql5.com"
#property version   "1.01"
#property strict

#include <Controls\Button.mqh>
#include <ChartObjects\ChartObjectsLines.mqh>
#include <Controls\Dialog.mqh>

// 性能优化配置
#define MAX_POSITIONS_BUFFER 1000    // 最大持仓缓冲区大小

// EA参数
input ulong MagicNumber = 0;         // 魔术号（0表示处理所有魔术号）

// 按钮对象
CButton btnSL;
CButton btnNewSL;
CButton btnCloseRange;

// 水平线名称（使用const优化字符串比较）
const string sl1_name = "sl_1";
const string sl2_name = "sl_2";
const string newsl_name = "new_sl";

// 状态变量
bool isCreatingHLine = false;    // 是否处于创建水平线模式
string currentHLineName = "";   // 当前水平线的名称

// 性能优化：预分配静态缓冲区
static ulong ticketsBuffer[MAX_POSITIONS_BUFFER];  // 静态订单票号缓冲区
static int ticketCount = 0;                        // 当前票号数量

// 按钮尺寸和位置设置（使用const优化）
const int button_width = 80;    // 按钮宽度
const int button_height = 35;    // 按钮高度
const int margin_horizontal = 92;  // 水平边距，增加此值使按钮往左移
const int margin_vertical = 80;    // 垂直边距，增加此值使按钮往上移

int OnInit()
{
   // 初始化静态缓冲区
   ticketCount = 0;
   
   // 启用鼠标移动事件
   ChartSetInteger(0, CHART_EVENT_MOUSE_MOVE, true);

   // 创建New SL按钮（上方按钮）- 垂直排列，位于第一个按钮上方
   if(!btnNewSL.Create(0, "btnNewSL", 0, margin_horizontal, margin_vertical + button_height + 10, button_width, button_height))
      return(INIT_FAILED);
   btnNewSL.Text("New SL");
   btnNewSL.Font("Arial");
   btnNewSL.FontSize(11);  // 增大字体
   btnNewSL.Color(clrBlack);
   btnNewSL.ColorBackground(clrMediumOrchid);
   btnNewSL.ColorBorder(clrGray);
   btnNewSL.Enable();

   // 设置按钮对齐方式为右下角
   ObjectSetInteger(0, "btnNewSL", OBJPROP_CORNER, CORNER_RIGHT_LOWER);

   // 创建SL按钮（底部按钮）
   if(!btnSL.Create(0, "btnSL", 0, margin_horizontal, margin_vertical, button_width, button_height))
      return(INIT_FAILED);
   btnSL.Text("Move SL");
   btnSL.Font("Arial");
   btnSL.FontSize(11);
   btnSL.Color(clrBlack);
   btnSL.ColorBackground(clrOrange);
   btnSL.ColorBorder(clrGray);
   btnSL.Enable();

   // 设置按钮对齐方式为右下角
   ObjectSetInteger(0, "btnSL", OBJPROP_CORNER, CORNER_RIGHT_LOWER);

   // 创建区间平仓按钮（最上方按钮）
   if(!btnCloseRange.Create(0, "btnCloseRange", 0, margin_horizontal, margin_vertical + (button_height + 10) * 2, button_width, button_height))
      return(INIT_FAILED);
   btnCloseRange.Text("区间平仓");
   btnCloseRange.Font("Arial");
   btnCloseRange.FontSize(11);
   btnCloseRange.Color(clrBlack);
   btnCloseRange.ColorBackground(clrRed);
   btnCloseRange.ColorBorder(clrGray);
   btnCloseRange.Enable();

   // 设置按钮对齐方式为右下角
   ObjectSetInteger(0, "btnCloseRange", OBJPROP_CORNER, CORNER_RIGHT_LOWER);

   // 立即重绘，不使用延迟
   ChartRedraw();
   
   // 显示初始化信息
   if(MagicNumber == 0)
       Print("EA初始化完成 - 保本订单移损工具 v1.01 | 处理所有魔术号");
   else
       Print("EA初始化完成 - 保本订单移损工具 v1.01 | 魔术号: ", MagicNumber);
   
   return(INIT_SUCCEEDED);
}

// 高性能内联函数：批量删除对象
inline void DeleteHorizontalLinesOptimized()
{
    // 批量删除，减少API调用次数
    ObjectDelete(0, sl1_name);
    ObjectDelete(0, sl2_name);
    ObjectDelete(0, newsl_name);
}

// 高性能优化版本：点击SL按钮，创建三条水平线
void OnClickSLButton()
{
    // 立即给用户反馈
    Print("正在创建水平线...");
    
    // 高效删除已存在的线（无需检查是否存在）
    DeleteHorizontalLinesOptimized();

    // 批量获取图表价格信息
    double high = ChartGetDouble(0, CHART_PRICE_MAX);
    double low = ChartGetDouble(0, CHART_PRICE_MIN);
    double middle_price = (high + low) * 0.5;  // 使用位运算优化除法

    // 预计算水平线间距
    double line_spacing = (high - low) * 0.05;

    // 批量创建三条水平线（减少函数调用开销）
    CreateHorizontalLineOptimized(sl1_name, middle_price, clrGold);
    CreateHorizontalLineOptimized(sl2_name, middle_price + line_spacing, clrGold);
    CreateHorizontalLineOptimized(newsl_name, middle_price - line_spacing, clrDeepPink);

    // 立即重绘
    ChartRedraw();
    Print("水平线创建完成");
}

// 高性能内联函数：快速数值比较
inline bool IsValidPrice(double price) { return price > 0.0; }
inline bool IsPriceInRange(double price, double lower, double upper) { return price >= lower && price <= upper; }

// 检查订单是否匹配当前品种和魔术号
inline bool IsMatchingPosition(ulong ticket)
{
    if(!PositionSelectByTicket(ticket))
        return false;
        
    // 检查品种
    string position_symbol = PositionGetString(POSITION_SYMBOL);
    if(StringCompare(position_symbol, _Symbol) != 0)
        return false;
        
    // 检查魔术号（0表示处理所有魔术号）
    if(MagicNumber != 0)
    {
        ulong position_magic = PositionGetInteger(POSITION_MAGIC);
        if(position_magic != MagicNumber)
            return false;
    }
    
    return true;
}

// 区间平仓函数 - 平仓sl1_price和sl2_price之间的订单
bool ClosePositionsInRange()
{
    // 立即给用户反馈
    Print("开始区间平仓...");
    
    // 获取三条线的价格位置
    double sl1_price = ObjectGetDouble(0, sl1_name, OBJPROP_PRICE);
    double sl2_price = ObjectGetDouble(0, sl2_name, OBJPROP_PRICE);
    
    // 检查水平线是否存在
    if(sl1_price == 0 || sl2_price == 0)
    {
        Print("错误: 未找到水平线，请先创建水平线");
        return false;
    }
    
    // 确保价格顺序正确
    double lower_price = MathMin(sl1_price, sl2_price);
    double upper_price = MathMax(sl1_price, sl2_price);
    
    // 收集区间内的订单
    ticketCount = 0;
    int total_positions = PositionsTotal();
    
    for(int i = 0; i < total_positions && ticketCount < MAX_POSITIONS_BUFFER; i++)
    {
        ulong ticket = PositionGetTicket(i);
        
        // 检查是否匹配当前品种和魔术号
        if(!IsMatchingPosition(ticket))
            continue;
            
        double open_price = PositionGetDouble(POSITION_PRICE_OPEN);
        
        // 检查开仓价格是否在区间内
        if(open_price >= lower_price && open_price <= upper_price)
        {
            ticketsBuffer[ticketCount] = ticket;
            ticketCount++;
        }
    }
    
    if(ticketCount == 0)
    {
        Print("区间内没有找到订单");
        return true;
    }
    
    // 平仓区间内的订单
    int closed = 0;
    for(int i = 0; i < ticketCount; i++)
    {
        ulong ticket = ticketsBuffer[i];
        if(PositionSelectByTicket(ticket))
        {
            double volume = PositionGetDouble(POSITION_VOLUME);
            ENUM_POSITION_TYPE pos_type = (ENUM_POSITION_TYPE)PositionGetInteger(POSITION_TYPE);
            
            MqlTradeRequest request = {};
            MqlTradeResult result = {};
            
            request.action = TRADE_ACTION_DEAL;
            request.position = ticket;
            request.symbol = _Symbol;
            request.volume = volume;
            request.type = (pos_type == POSITION_TYPE_BUY) ? ORDER_TYPE_SELL : ORDER_TYPE_BUY;
            request.price = (pos_type == POSITION_TYPE_BUY) ? 
                           SymbolInfoDouble(_Symbol, SYMBOL_BID) : 
                           SymbolInfoDouble(_Symbol, SYMBOL_ASK);
            request.deviation = 5;
            
            if(OrderSend(request, result))
            {
                if(result.retcode == TRADE_RETCODE_DONE)
                {
                    closed++;
                }
            }
        }
    }
    
    Print("区间平仓完成，共平仓 ", closed, " 个订单");
    return true;
}

// 检查订单是否已经保本
bool IsBreakevenPosition(ulong ticket)
{
    if(!PositionSelectByTicket(ticket))
        return false;

    double open_price = PositionGetDouble(POSITION_PRICE_OPEN);
    double current_sl = PositionGetDouble(POSITION_SL);
    ENUM_POSITION_TYPE pos_type = (ENUM_POSITION_TYPE)PositionGetInteger(POSITION_TYPE);

    if(current_sl <= 0)
        return false; // 没有止损，不算保本

    if(pos_type == POSITION_TYPE_BUY)
    {
        // 买单：止损价格 >= 开仓价格 才算保本
        return current_sl >= open_price;
    }
    else // POSITION_TYPE_SELL
    {
        // 卖单：止损价格 <= 开仓价格 才算保本
        return current_sl <= open_price;
    }
}

// 点击New SL按钮，移动止损（高性能优化版本）
void OnClickNewSLButton()
{
    // 立即给用户反馈
    Print("开始移动保本订单止损...");

    // 获取三条线的价格位置（批量获取减少API调用）
    double sl1_price = ObjectGetDouble(0, sl1_name, OBJPROP_PRICE);
    double sl2_price = ObjectGetDouble(0, sl2_name, OBJPROP_PRICE);
    double new_sl_price = ObjectGetDouble(0, newsl_name, OBJPROP_PRICE);

    // 使用内联函数进行快速验证
    if(!IsValidPrice(sl1_price) || !IsValidPrice(sl2_price) || !IsValidPrice(new_sl_price))
    {
        Print("错误: 未找到所有水平线");
        return;
    }

    // 确保sl1_price和sl2_price的顺序正确（使用内联优化）
    double lower_price = MathMin(sl1_price, sl2_price);
    double upper_price = MathMax(sl1_price, sl2_price);

    // 重置静态缓冲区计数器
    ticketCount = 0;
    int total_positions = PositionsTotal();

    // 预先检查缓冲区容量
    if(total_positions > MAX_POSITIONS_BUFFER)
    {
        Print("警告: 持仓数量超过缓冲区限制，将处理前", MAX_POSITIONS_BUFFER, "个持仓");
        total_positions = MAX_POSITIONS_BUFFER;
    }

    // 扫描保本订单
    for(int i = 0; i < total_positions && ticketCount < MAX_POSITIONS_BUFFER; i++)
    {
        ulong ticket = PositionGetTicket(i);

        // 检查是否匹配当前品种和魔术号
        if(!IsMatchingPosition(ticket))
            continue;

        double current_sl = PositionGetDouble(POSITION_SL);
        double open_price = PositionGetDouble(POSITION_PRICE_OPEN);
        ENUM_POSITION_TYPE pos_type = (ENUM_POSITION_TYPE)PositionGetInteger(POSITION_TYPE);

        // 检查是否为保本订单且止损在指定范围内
        bool is_breakeven = IsBreakevenPosition(ticket);
        bool in_range = (current_sl > 0 && current_sl >= lower_price && current_sl <= upper_price);

        if(is_breakeven && in_range)
        {
            ticketsBuffer[ticketCount] = ticket;
            ticketCount++;
        }
    }

    // 批量处理收集到的所有订单（使用静态缓冲区）
    int modified = 0;
    for(int i = 0; i < ticketCount; i++)
    {
        ulong ticket = ticketsBuffer[i];
        if(PositionSelectByTicket(ticket))
        {
            // 缓存订单信息，减少API调用
            double current_sl = PositionGetDouble(POSITION_SL);
            double open_price = PositionGetDouble(POSITION_PRICE_OPEN);
            double tp_price = PositionGetDouble(POSITION_TP);

            // 修改止损到新位置
            if(TradeModifyOptimized(ticket, open_price, new_sl_price, tp_price))
            {
                modified++;
            }
            else
            {
                Print("订单 ", ticket, " 修改失败");
            }
        }
    }

    Print("操作完成。共修改了 ", modified, " 个保本订单的止损");
}

// 高性能优化版本：创建水平线
inline void CreateHorizontalLineOptimized(const string name, const double price, const color clr)
{
    // 直接创建，无需检查是否存在（已在调用前删除）
    ObjectCreate(0, name, OBJ_HLINE, 0, 0, price);

    // 批量设置属性，减少API调用
    ObjectSetInteger(0, name, OBJPROP_COLOR, clr);
    ObjectSetInteger(0, name, OBJPROP_STYLE, STYLE_DASH);
    ObjectSetInteger(0, name, OBJPROP_WIDTH, 2);
    ObjectSetInteger(0, name, OBJPROP_BACK, false);
    ObjectSetInteger(0, name, OBJPROP_SELECTABLE, true);
    ObjectSetInteger(0, name, OBJPROP_SELECTED, true);
    ObjectSetInteger(0, name, OBJPROP_TIMEFRAMES, OBJ_ALL_PERIODS);
    ObjectSetInteger(0, name, OBJPROP_ZORDER, 100);
}

// 高性能优化版本：修改订单止损和止盈
inline bool TradeModifyOptimized(ulong ticket, double entry_price, double sl, double tp)
{
    // 快速检查止损是否已经是目标值（避免重复修改）
    double current_sl = PositionGetDouble(POSITION_SL);
    if(MathAbs(current_sl - sl) < _Point)
    {
        return true; // 无需修改，返回成功
    }

    // 使用静态结构体避免重复初始化
    static MqlTradeRequest request;
    static MqlTradeResult result;

    // 重置结构体（比{}初始化更高效）
    ZeroMemory(request);
    ZeroMemory(result);

    request.action = TRADE_ACTION_SLTP;
    request.position = ticket;
    request.symbol = _Symbol;  // 直接使用_Symbol，避免API调用
    request.sl = sl;
    request.tp = tp;
    request.deviation = 5;

    bool success = OrderSend(request, result);

    // 简化的结果处理（减少字符串操作）
    return (success && result.retcode == TRADE_RETCODE_DONE);
}

// 高性能事件处理函数
void OnChartEvent(const int id, const long &lparam, const double &dparam, const string &sparam)
{
    // 优化按钮点击事件处理（使用字符串哈希比较）
    if(id == CHARTEVENT_OBJECT_CLICK)
    {
        // 使用StringCompare进行高效字符串比较
        if(StringCompare(sparam, "btnSL") == 0)
        {
            OnClickSLButton();
            return;
        }
        else if(StringCompare(sparam, "btnNewSL") == 0)
        {
            OnClickNewSLButton();
            return;
        }
        else if(StringCompare(sparam, "btnCloseRange") == 0)
        {
            ClosePositionsInRange();
            return;
        }
    }

    // 优化图表大小变化事件处理
    if(id == CHARTEVENT_CHART_CHANGE)
    {
        // 批量设置按钮属性，减少API调用
        btnSL.Alignment(WND_ALIGN_RIGHT | WND_ALIGN_BOTTOM, 0, 0, margin_horizontal, margin_vertical);
        btnSL.Width(button_width);
        btnSL.Height(button_height);

        btnNewSL.Alignment(WND_ALIGN_RIGHT | WND_ALIGN_BOTTOM, 0, 0, margin_horizontal, margin_vertical + button_height + 10);
        btnNewSL.Width(button_width);
        btnNewSL.Height(button_height);

        btnCloseRange.Alignment(WND_ALIGN_RIGHT | WND_ALIGN_BOTTOM, 0, 0, margin_horizontal, margin_vertical + (button_height + 10) * 2);
        btnCloseRange.Width(button_width);
        btnCloseRange.Height(button_height);

        // 立即重绘
        ChartRedraw();
        return;
    }

    // 优化鼠标移动处理（创建水平线模式）
    if(isCreatingHLine && id == CHARTEVENT_MOUSE_MOVE)
    {
        static double price = 0;
        static datetime time = 0;
        static int sub_window = 0;

        if(ChartXYToTimePrice(0, (int)lparam, (int)dparam, sub_window, time, price))
        {
            ObjectMove(0, currentHLineName, 0, 0, price);
            // 立即重绘，不使用延迟
            ChartRedraw();
        }
        return;
    }

    // 优化鼠标点击事件处理（固定线）
    if(isCreatingHLine && id == CHARTEVENT_CLICK)
    {
        // 使用高效字符串比较避免误点击按钮
        if(StringCompare(sparam, "btnSL") == 0 || StringCompare(sparam, "btnNewSL") == 0 || StringCompare(sparam, "btnCloseRange") == 0)
            return;

        isCreatingHLine = false;
        ObjectSetInteger(0, currentHLineName, OBJPROP_SELECTED, 0);
        ChartRedraw();
    }
}

void OnDeinit(const int reason)
{
    // 销毁按钮对象
    btnSL.Destroy(reason);
    btnNewSL.Destroy(reason);
    btnCloseRange.Destroy(reason);

    // 删除水平线对象
    DeleteHorizontalLinesOptimized();

    // 清理静态缓冲区
    ticketCount = 0;
    ZeroMemory(ticketsBuffer);

    ChartRedraw();
}
